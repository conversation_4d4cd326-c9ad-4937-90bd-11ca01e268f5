#!/usr/bin/env python3
"""
Simple Plot Generator for ANMS Documentation
Creates basic performance plots using matplotlib with minimal dependencies.
"""

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
import os

# Ensure output directory exists
os.makedirs('plots', exist_ok=True)

def create_performance_comparison():
    """Create a simple performance comparison bar chart."""
    try:
        methods = ['ANMS\n(Our Work)', 'Traditional\nIDS', 'Deep Learning\nIDS', 'Ensemble\nMethods']
        accuracy = [99.95, 94.2, 96.8, 97.5]
        colors = ['#2E8B57', '#FF6347', '#4169E1', '#DAA520']
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(methods, accuracy, color=colors, edgecolor='black', linewidth=1)
        
        # Add value labels on bars
        for bar, acc in zip(bars, accuracy):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2, 
                    f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=12)
        
        plt.title('ANMS Performance Comparison with State-of-the-Art Methods', 
                 fontsize=14, fontweight='bold', pad=20)
        plt.ylabel('Accuracy (%)', fontsize=12)
        plt.ylim(90, 102)
        plt.grid(True, alpha=0.3, axis='y')
        
        # Add a text box with key metrics
        textstr = 'ANMS Key Metrics:\n• Accuracy: 99.95%\n• Response Time: 15.2ms\n• Throughput: 1000+ req/sec'
        props = dict(boxstyle='round', facecolor='lightblue', alpha=0.8)
        plt.text(0.02, 0.98, textstr, transform=plt.gca().transAxes, fontsize=10,
                verticalalignment='top', bbox=props)
        
        plt.tight_layout()
        plt.savefig('plots/performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ Performance comparison plot created")
        return True
    except Exception as e:
        print(f"✗ Error creating performance comparison: {e}")
        return False

def create_agent_metrics():
    """Create agent performance metrics chart."""
    try:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
        
        # Monitoring Agent
        monitoring_metrics = ['Data\nCollection', 'Feature\nExtraction', 'Preprocessing\nAccuracy', 'Real-time\nProcessing']
        monitoring_values = [99.8, 95.2, 98.7, 94.5]
        
        bars1 = ax1.bar(monitoring_metrics, monitoring_values, color='lightgreen', edgecolor='darkgreen')
        ax1.set_title('Monitoring Agent Performance', fontweight='bold')
        ax1.set_ylabel('Performance (%)')
        ax1.set_ylim(90, 100)
        ax1.grid(True, alpha=0.3)
        
        for bar, val in zip(bars1, monitoring_values):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                    f'{val:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # Random Forest Performance
        rf_metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
        rf_values = [99.95, 99.83, 99.94, 99.88]
        
        bars2 = ax2.bar(rf_metrics, rf_values, color='lightcoral', edgecolor='darkred')
        ax2.set_title('Random Forest Performance', fontweight='bold')
        ax2.set_ylabel('Performance (%)')
        ax2.set_ylim(99, 100)
        ax2.grid(True, alpha=0.3)
        
        for bar, val in zip(bars2, rf_values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{val:.2f}%', ha='center', va='bottom', fontweight='bold')
        
        # Isolation Forest Performance
        iso_metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
        iso_values = [89.0, 48.0, 100.0, 65.0]
        
        bars3 = ax3.bar(iso_metrics, iso_values, color='plum', edgecolor='purple')
        ax3.set_title('Isolation Forest Performance', fontweight='bold')
        ax3.set_ylabel('Performance (%)')
        ax3.set_ylim(0, 105)
        ax3.grid(True, alpha=0.3)
        
        for bar, val in zip(bars3, iso_values):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2, 
                    f'{val:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # Decision Agent Performance
        decision_metrics = ['Overall\nAccuracy', 'Consensus\nRate', 'Response\nTime (ms)', 'Decision\nConfidence']
        decision_values = [94.5, 87.3, 15.2, 92.1]
        
        bars4 = ax4.bar(decision_metrics, decision_values, color='gold', edgecolor='orange')
        ax4.set_title('Decision Agent Performance', fontweight='bold')
        ax4.set_ylabel('Performance (% / ms)')
        ax4.set_ylim(0, 100)
        ax4.grid(True, alpha=0.3)
        
        for bar, val in zip(bars4, decision_values):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2, 
                    f'{val:.1f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('plots/agent_performance_metrics.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ Agent performance metrics plot created")
        return True
    except Exception as e:
        print(f"✗ Error creating agent metrics: {e}")
        return False

def create_feature_importance():
    """Create feature importance horizontal bar chart."""
    try:
        features = ['Flow Duration', 'Fwd Packet Length Mean', 'Flow Bytes/s', 
                   'Bwd Packet Length Mean', 'Flow IAT Mean', 'Fwd IAT Mean',
                   'Packet Length Mean', 'Total Fwd Packets']
        importance = [0.145, 0.132, 0.118, 0.095, 0.087, 0.076, 0.069, 0.058]
        
        plt.figure(figsize=(10, 6))
        bars = plt.barh(features, importance, color='skyblue', edgecolor='navy')
        
        plt.title('Top 8 Feature Importance (Random Forest)', fontweight='bold', fontsize=14)
        plt.xlabel('Importance Score', fontsize=12)
        plt.grid(True, alpha=0.3, axis='x')
        
        # Add value labels
        for bar, imp in zip(bars, importance):
            plt.text(bar.get_width() + 0.005, bar.get_y() + bar.get_height()/2, 
                    f'{imp:.3f}', ha='left', va='center', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('plots/feature_importance.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ Feature importance plot created")
        return True
    except Exception as e:
        print(f"✗ Error creating feature importance: {e}")
        return False

def create_confusion_matrix_visualization():
    """Create confusion matrix visualization."""
    try:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Random Forest Confusion Matrix
        rf_cm = np.array([[20, 0], [0, 180]])
        im1 = ax1.imshow(rf_cm, interpolation='nearest', cmap='Blues')
        ax1.set_title('Random Forest\nConfusion Matrix', fontweight='bold')
        
        # Add text annotations
        for i in range(2):
            for j in range(2):
                ax1.text(j, i, rf_cm[i, j], ha="center", va="center", 
                        color="white" if rf_cm[i, j] > rf_cm.max()/2 else "black",
                        fontsize=20, fontweight='bold')
        
        ax1.set_xticks([0, 1])
        ax1.set_yticks([0, 1])
        ax1.set_xticklabels(['Anomaly', 'Normal'])
        ax1.set_yticklabels(['Anomaly', 'Normal'])
        ax1.set_xlabel('Predicted Label')
        ax1.set_ylabel('True Label')
        
        # Isolation Forest Confusion Matrix
        iso_cm = np.array([[20, 0], [22, 158]])
        im2 = ax2.imshow(iso_cm, interpolation='nearest', cmap='Reds')
        ax2.set_title('Isolation Forest\nConfusion Matrix', fontweight='bold')
        
        # Add text annotations
        for i in range(2):
            for j in range(2):
                ax2.text(j, i, iso_cm[i, j], ha="center", va="center", 
                        color="white" if iso_cm[i, j] > iso_cm.max()/2 else "black",
                        fontsize=20, fontweight='bold')
        
        ax2.set_xticks([0, 1])
        ax2.set_yticks([0, 1])
        ax2.set_xticklabels(['Anomaly', 'Normal'])
        ax2.set_yticklabels(['Anomaly', 'Normal'])
        ax2.set_xlabel('Predicted Label')
        ax2.set_ylabel('True Label')
        
        plt.tight_layout()
        plt.savefig('plots/confusion_matrices.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ Confusion matrices plot created")
        return True
    except Exception as e:
        print(f"✗ Error creating confusion matrices: {e}")
        return False

def main():
    """Generate all simple plots."""
    print("=" * 50)
    print("ANMS Simple Plot Generator")
    print("=" * 50)
    
    success_count = 0
    total_plots = 4
    
    if create_performance_comparison():
        success_count += 1
    if create_agent_metrics():
        success_count += 1
    if create_feature_importance():
        success_count += 1
    if create_confusion_matrix_visualization():
        success_count += 1
    
    print("=" * 50)
    print(f"✅ {success_count}/{total_plots} plots generated successfully!")
    print("=" * 50)
    print("\nGenerated plots:")
    print("1. performance_comparison.png")
    print("2. agent_performance_metrics.png")
    print("3. feature_importance.png")
    print("4. confusion_matrices.png")
    print(f"\nAll files saved in: {os.path.abspath('plots')}")

if __name__ == "__main__":
    main()
