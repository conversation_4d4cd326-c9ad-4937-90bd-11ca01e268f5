<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ANMS Dashboard</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <header>
        <h1 class="title">MA-ANMS</h1>
        <p id="title">MULTI-AGENT AUTONOMOUS NETWORK MONITORING SYSTEM</p>
    </header>

    <main>
        <section class="dashboard-section">
            <h2>System Overview</h2>
            <div class="kpi-grid">
                <div class="kpi-card">
                    <h3>Status</h3>
                    <p id="system-status">Online</p>
                </div>
                <div class="kpi-card">
                    <h3>Anomalies (Last Hour)</h3>
                    <p id="anomalies-last-hour">0</p>
                </div>
                <div class="kpi-card">
                    <h3>Total Logs</h3>
                    <p id="total-logs">0</p>
                </div>
                <div class="kpi-card">
                    <h3>Last Update</h3>
                    <p id="last-update">N/A</p>
                </div>
            </div>
        </section>

        <h2>Network Topology</h2>
            <div class="topology-container">
                <img src="/static/images/university_network_pt.png" alt="University Network Topology Diagram">
            </div>

                
            </div>
        </section>

        <section class="dashboard-section">
            <h2>Real-time Anomaly Logs</h2>
            <button onclick="downloadLogs()">Download All Logs</button>
            <div class="log-container">
                <table id="anomaly-logs-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Status</th>
                            <th>S-Pred</th>
                            <th>U-Pred</th>
                            <th>S-Proba (Anomaly)</th>
                            <th>U-Score</th>
                            <th>XAI Explanation</th>
                            
                        </tr>
                    </thead>
                    <tbody>
                        </tbody>
                </table>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2025 MA-ANMS</p>
    </footer>

    <script>
        // Function to fetch and update logs
        function updateLogs() {
            $.getJSON('/get_latest_logs', function(data) {
                var tbody = $('#anomaly-logs-table tbody');
                tbody.empty(); // Clear existing logs

                let anomalyCount = 0;
                let totalLogs = data.length;

                data.forEach(function(log) {
                    if (log.is_anomaly) {
                        anomalyCount++;
                    }
                    var xaiHtml = 'N/A';
                    if (log.xai_explanation && log.xai_explanation.top_features && log.xai_explanation.top_features.length > 0) {
                        xaiHtml = '<ul>';
                        log.xai_explanation.top_features.forEach(function(feat) {
                            xaiHtml += `<li>${feat.name}: ${feat.value.toFixed(4)}</li>`;
                        });
                        xaiHtml += '</ul>';
                    }

                    var row = `<tr class="${log.is_anomaly ? 'anomaly-row' : 'normal-row'}">
                        <td>${new Date(log.timestamp).toLocaleTimeString()}</td>
                        <td>${log.is_anomaly ? '<span class="status-anomaly">ANOMALY</span>' : '<span class="status-normal">NORMAL</span>'}</td>
                        <td>${log.supervised_prediction}</td>
                        <td>${log.unsupervised_prediction}</td>
                        <td>${log.supervised_proba_anomaly.toFixed(4)}</td>
                        <td>${log.unsupervised_anomaly_score.toFixed(4)}</td>
                        <td>${xaiHtml}</td>
                        }...</td>
                    </tr>`;
                    tbody.append(row);
                });

                // Update KPIs
                $('#anomalies-last-hour').text(anomalyCount); // Simple count for now, not hourly
                $('#total-logs').text(totalLogs);
                $('#last-update').text(new Date().toLocaleTimeString());
            });
        }

        // Function to trigger a simulated detection
        function triggerDetection() {
            $.get('/detect_anomaly', function(data) {
                if (data.status === 'success') {
                    console.log("Detection simulated successfully.");
                    updateLogs(); // Update logs after a new detection
                } else {
                    console.error("Error simulating detection:", data.message);
                }
            });
        }

        // Function to download logs
        function downloadLogs() {
            window.location.href = '/download_logs';
        }

        // Periodically update logs and trigger new detections
        $(document).ready(function() {
            // Initial log fetch
            updateLogs();
            // Simulate a new detection every 5 seconds
            setInterval(triggerDetection, 5000);
            // Update logs display every 5 seconds (this will also happen after triggerDetection)
            // If you only want to update on new detection, remove this:
            // setInterval(updateLogs, 5000);
        });
    </script>
</body>
</html>