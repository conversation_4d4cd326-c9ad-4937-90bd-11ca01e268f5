# app.py
from flask import Flask, render_template, jsonify, send_file
import pandas as pd
import numpy as np
import io
import os
import logging
# from collections import Counter  # Not used

# Local imports (ensure correct paths based on your project structure)
from decision_agent import DecisionAgent
# Ensure xai_explainer.py is correctly placed relative to decision_agent.py or handled by sys.path
# from src.utils.xai_explainer import XAI_Explainer # This is imported within DecisionAgent now

from flask import Flask, render_template, jsonify
import os

app = Flask(__name__)
# Ensure this path correctly points to where you saved your image
IMAGE_DIR = os.path.join(app.root_path, 'static', 'images')

# Configure logging for Flask app
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)

# --- Global Variables for Agents & Data ---
decision_agent = None
feature_names = None
realtime_logs = [] # Stores recent anomaly detection logs
MAX_LOG_ENTRIES = float('inf') # Allow unlimited log entries

# --- Initialization Function ---
def initialize_agents():
    global decision_agent, feature_names

    logger.info("Initializing ANMS Agents...")
    try:
        # Load a small batch of processed data to infer feature names and for XAI background
        # Use dummy data for demonstration
        processed_data_path = "data/processed/dummy_cicids_data.csv"
        try:
            sample_df = pd.read_csv(processed_data_path, nrows=100) # Load first 100 rows to get features
            if 'Label' in sample_df.columns:
                feature_names = sample_df.drop('Label', axis=1).columns.tolist()
                X_train_for_xai = sample_df.drop('Label', axis=1) # Use for XAI background
            else:
                feature_names = sample_df.columns.tolist()
                X_train_for_xai = sample_df
            logger.info(f"Feature names loaded from {processed_data_path}: {feature_names[:5]}...")

            background_data_for_shap = X_train_for_xai.sample(min(50, len(X_train_for_xai)), random_state=42)
            logger.info(f"XAI background data shape: {background_data_for_shap.shape}")


        except FileNotFoundError:
            logger.warning(f"Processed data file not found at {processed_data_path}. Using dummy feature names.")
            feature_names = [f'feature_{i}' for i in range(60)] # Fallback for dummy data
            background_data_for_shap = pd.DataFrame(np.random.rand(50, 60), columns=feature_names) # Dummy background

        decision_agent = DecisionAgent(feature_names=feature_names)

        # Initialize XAI Explainer AFTER DecisionAgent has loaded its models
        # We need the RandomForest model object from the SupervisedAgent to initialize XAI
        if decision_agent.supervised_agent.model:
            decision_agent.initialize_xai_explainer(
                model=decision_agent.supervised_agent.model,
                background_data_for_shap=background_data_for_shap,
                target_class_index=0 # Explain anomalies (class 0)
            )
        else:
            logger.error("Supervised model not loaded, XAI Explainer cannot be fully initialized.")

        logger.info("ANMS Agents initialized successfully.")
    except Exception as e:
        logger.error(f"Failed to initialize ANMS Agents: {e}")
        # Optionally, re-raise or set a flag to indicate system is not ready

# --- Flask Routes ---

@app.route('/')
def index():
    # Make sure the filename here matches the one you saved from Packet Tracer!
    return render_template('index.html', topology_image_url="/static/images/university_network_pt.png")

@app.route('/detect_anomaly')
def simulate_detection():
    """
    Simulates real-time anomaly detection by generating a random instance
    and passing it through the decision agent.
    """
    if decision_agent is None or feature_names is None:
        return jsonify({"status": "error", "message": "Agents not initialized."}), 500

    # Simulate receiving a new network traffic instance
    

    # Generate simple simulated data matching our training data
    if np.random.rand() < 0.3:  # 30% chance of simulating an anomaly
        logger.info("Simulating anomaly traffic")
        # Generate anomaly data (higher values)
        new_instance_data = pd.DataFrame(
            np.random.normal(loc=500, scale=50, size=(1, len(feature_names))),
            columns=feature_names
        )
    else:
        logger.info("Simulating normal traffic")
        # Generate normal data (lower values)
        new_instance_data = pd.DataFrame(
            np.random.normal(loc=100, scale=20, size=(1, len(feature_names))),
            columns=feature_names
        )

    # Ensure all values are non-negative
    new_instance_data = new_instance_data.clip(lower=0)

    log_entry = decision_agent.make_decision(new_instance_data)
    realtime_logs.insert(0, log_entry) # Add to the front for most recent first
    if len(realtime_logs) > MAX_LOG_ENTRIES:
        realtime_logs.pop() # Remove oldest entry

    logger.info(f"Simulated detection: {log_entry['final_prediction']} (Anomaly: {log_entry['is_anomaly']})")
    return jsonify({"status": "success", "log_entry": log_entry})

@app.route('/get_latest_logs')
def get_latest_logs():
    """Endpoint to fetch logs for dynamic update on dashboard."""
    return jsonify(realtime_logs)

@app.route('/download_logs')
def download_logs():
    """Allows downloading of all accumulated logs."""
    if not realtime_logs:
        return jsonify({"message": "No logs to download."}), 200

    df_logs = pd.DataFrame(realtime_logs)
    # Flatten XAI explanation for better CSV export if needed
    if 'xai_explanation' in df_logs.columns:
        df_logs['xai_base_value'] = df_logs['xai_explanation'].apply(lambda x: x['base_value'] if x else None)
        df_logs['xai_top_features'] = df_logs['xai_explanation'].apply(lambda x: x['top_features'] if x else None)
        df_logs = df_logs.drop('xai_explanation', axis=1)

    output = io.StringIO()
    df_logs.to_csv(output, index=False)
    output.seek(0)

    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8')),
        mimetype='text/csv',
        as_attachment=True,
        download_name='anomaly_logs.csv'
    )

# --- Run Initialization on App Startup ---
with app.app_context():
    initialize_agents()

if __name__ == '__main__':
    # Ensure models directory exists for agent initialization
    if not os.path.exists("models/"):
        os.makedirs("models/")
    if not os.path.exists("data/processed/"):
        os.makedirs("data/processed/")


    app.run(debug=True, host='0.0.0.0', port=5000)